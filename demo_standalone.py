#!/usr/bin/env python3
"""
独立演示脚本 - 展示工具发现和注册功能，不依赖模型
"""
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tool_registry import ToolRegistry

logging.basicConfig(level=logging.INFO)

def demo_tool_discovery():
    """演示工具发现功能"""
    print("🔍 演示工具发现功能")
    print("-" * 40)
    
    # 创建工具注册器
    registry = ToolRegistry()
    
    # 扫描工具
    discovered_tools = registry.scan_tools()
    
    print(f"✅ 发现 {len(discovered_tools)} 个工具:")
    for tool_name, tool_info in discovered_tools.items():
        print(f"  📋 {tool_name}")
        print(f"     描述: {tool_info['description'][:60]}...")
        print(f"     文件: {tool_info['file_path']}")
        
        # 显示参数信息
        if 'input_schema' in tool_info and 'properties' in tool_info['input_schema']:
            params = tool_info['input_schema']['properties']
            if params:
                print(f"     参数: {', '.join(params.keys())}")
        print()
    
    # 更新配置
    registry.update_servers_config()
    print(f"✅ 配置文件已更新")
    
    return discovered_tools

def demo_config_generation():
    """演示配置生成"""
    print("\n⚙️  演示配置生成")
    print("-" * 40)
    
    # 读取生成的配置
    with open("servers_config.json", 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("✅ 生成的服务器配置:")
    for server_name, server_config in config["mcpServers"].items():
        print(f"  🖥️  {server_name}:")
        print(f"     命令: {server_config['command']}")
        print(f"     参数: {' '.join(server_config['args'])}")
        print()

def demo_tool_categories():
    """演示工具分类"""
    print("📂 工具分类演示")
    print("-" * 40)
    
    registry = ToolRegistry()
    discovered_tools = registry.registered_tools
    
    # 按文件分类工具
    categories = {}
    for tool_name, tool_info in discovered_tools.items():
        file_path = tool_info['file_path']
        category = Path(file_path).stem
        
        if category not in categories:
            categories[category] = []
        categories[category].append((tool_name, tool_info))
    
    for category, tools in categories.items():
        print(f"📁 {category} ({len(tools)} 个工具):")
        for tool_name, tool_info in tools:
            print(f"   • {tool_name}: {tool_info['description'][:40]}...")
        print()

def demo_tool_usage_examples():
    """演示工具使用示例"""
    print("💡 工具使用示例")
    print("-" * 40)
    
    examples = [
        {
            "query": "查看当前目录的文件",
            "tool": "list_directory",
            "args": {"dir_path": ".", "show_hidden": False},
            "description": "列出当前目录中的所有文件和文件夹"
        },
        {
            "query": "读取README文件",
            "tool": "read_file", 
            "args": {"file_path": "./README.md"},
            "description": "读取README.md文件的内容"
        },
        {
            "query": "查看系统信息",
            "tool": "get_system_info",
            "args": {},
            "description": "获取操作系统、处理器等基本信息"
        },
        {
            "query": "检查CPU使用情况",
            "tool": "get_cpu_info",
            "args": {},
            "description": "获取CPU使用率和频率信息"
        },
        {
            "query": "查看内存使用情况",
            "tool": "get_memory_info", 
            "args": {},
            "description": "获取内存和交换空间使用情况"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. 用户问题: \"{example['query']}\"")
        print(f"   AI会调用: {example['tool']}")
        print(f"   参数: {json.dumps(example['args'], ensure_ascii=False)}")
        print(f"   功能: {example['description']}")
        print()

def demo_file_structure():
    """演示项目文件结构"""
    print("📁 项目文件结构")
    print("-" * 40)
    
    structure = {
        "qwen3/": {
            "qwen3_test.py": "Qwen3模型封装类",
            "qwen3_mcp_client.py": "主要的MCP客户端",
            "tool_registry.py": "工具自动发现和注册系统",
            "start_qwen3_client.py": "启动脚本",
            "servers_config.json": "MCP服务器配置文件",
            "mcp_server_open_file.py": "文档打开工具服务器",
            "tools/": {
                "__init__.py": "工具包初始化",
                "file_operations.py": "文件操作工具集",
                "system_info.py": "系统信息工具集"
            },
            "Qwen3-8B/": "Qwen3模型文件目录"
        }
    }
    
    def print_structure(struct, indent=0):
        for name, desc in struct.items():
            if isinstance(desc, dict):
                print("  " * indent + f"📁 {name}")
                print_structure(desc, indent + 1)
            else:
                print("  " * indent + f"📄 {name} - {desc}")
    
    print_structure(structure)

def demo_workflow():
    """演示工作流程"""
    print("\n🔄 工作流程演示")
    print("-" * 40)
    
    steps = [
        "1. 🔍 启动时自动扫描 tools/ 目录",
        "2. 📋 识别带有 @tool 装饰器的函数",
        "3. ⚙️  自动生成 MCP 服务器配置",
        "4. 🚀 启动对应的 MCP 服务器进程",
        "5. 🤖 Qwen3 模型加载并准备对话",
        "6. 💬 用户输入问题",
        "7. 🧠 AI 分析问题并选择合适工具",
        "8. 🔧 调用工具并获取结果",
        "9. 📝 AI 整理结果并回复用户"
    ]
    
    for step in steps:
        print(step)

def main():
    """主演示函数"""
    print("🎭 Qwen3 MCP Client 功能演示")
    print("=" * 60)
    print("这是一个演示版本，展示工具发现和注册功能")
    print("实际版本需要安装完整依赖并加载Qwen3模型")
    print("=" * 60)
    
    try:
        # 演示工具发现
        demo_tool_discovery()
        
        # 演示配置生成
        demo_config_generation()
        
        # 演示工具分类
        demo_tool_categories()
        
        # 演示使用示例
        demo_tool_usage_examples()
        
        # 演示文件结构
        demo_file_structure()
        
        # 演示工作流程
        demo_workflow()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("\n📋 要运行完整版本:")
        print("1. 安装依赖: python install_dependencies.py")
        print("2. 确保Qwen3-8B模型已下载到 ./Qwen3-8B/ 目录")
        print("3. 启动客户端: python start_qwen3_client.py")
        print("\n💡 添加新工具:")
        print("- 在 tools/ 目录创建新的 .py 文件")
        print("- 使用 @tool 装饰器标记工具函数")
        print("- 重启客户端即可自动发现新工具")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
