#!/usr/bin/env python3
"""
网络化文件操作工具服务器
"""
import os
import shutil
from pathlib import Path
from typing import Dict, Any

from network_tool_server import create_tool_server

# 创建工具服务器
server = create_tool_server(
    server_name="file_operations",
    port=8001,
    description="文件操作工具服务器 - 提供文件读写、目录操作等功能"
)

@server.tool("读取文件内容")
def read_file(file_path: str) -> Dict[str, Any]:
    """读取指定文件的内容
    
    Args:
        file_path: 要读取的文件路径
        
    Returns:
        包含文件内容的字典
    """
    try:
        if not os.path.exists(file_path):
            return {"success": False, "error": f"文件不存在: {file_path}"}
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        return {
            "success": True,
            "file_path": file_path,
            "content": content,
            "size": len(content),
            "encoding": "utf-8"
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("写入文件内容")
def write_file(file_path: str, content: str, mode: str = "w") -> Dict[str, Any]:
    """写入内容到指定文件
    
    Args:
        file_path: 目标文件路径
        content: 要写入的内容
        mode: 写入模式 ('w' 覆盖, 'a' 追加)
        
    Returns:
        操作结果字典
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, mode, encoding='utf-8') as f:
            f.write(content)
            
        return {
            "success": True,
            "file_path": file_path,
            "bytes_written": len(content.encode('utf-8')),
            "mode": mode
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("列出目录内容")
def list_directory(dir_path: str, show_hidden: bool = False) -> Dict[str, Any]:
    """列出指定目录的内容
    
    Args:
        dir_path: 目录路径
        show_hidden: 是否显示隐藏文件
        
    Returns:
        目录内容列表
    """
    try:
        if not os.path.exists(dir_path):
            return {"success": False, "error": f"目录不存在: {dir_path}"}
            
        if not os.path.isdir(dir_path):
            return {"success": False, "error": f"路径不是目录: {dir_path}"}
            
        items = []
        for item in os.listdir(dir_path):
            if not show_hidden and item.startswith('.'):
                continue
                
            item_path = os.path.join(dir_path, item)
            item_info = {
                "name": item,
                "path": item_path,
                "is_file": os.path.isfile(item_path),
                "is_directory": os.path.isdir(item_path),
                "size": os.path.getsize(item_path) if os.path.isfile(item_path) else 0,
                "modified_time": os.path.getmtime(item_path)
            }
            items.append(item_info)
            
        return {
            "success": True,
            "directory": dir_path,
            "items": items,
            "count": len(items)
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("删除文件或目录")
def delete_path(path: str, recursive: bool = False) -> Dict[str, Any]:
    """删除指定的文件或目录
    
    Args:
        path: 要删除的路径
        recursive: 是否递归删除目录
        
    Returns:
        删除操作结果
    """
    try:
        if not os.path.exists(path):
            return {"success": False, "error": f"路径不存在: {path}"}
            
        if os.path.isfile(path):
            os.remove(path)
            return {"success": True, "action": "file_deleted", "path": path}
        elif os.path.isdir(path):
            if recursive:
                shutil.rmtree(path)
                return {"success": True, "action": "directory_deleted", "path": path}
            else:
                os.rmdir(path)  # 只删除空目录
                return {"success": True, "action": "empty_directory_deleted", "path": path}
        else:
            return {"success": False, "error": f"未知路径类型: {path}"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("创建目录")
def create_directory(dir_path: str, parents: bool = True) -> Dict[str, Any]:
    """创建目录
    
    Args:
        dir_path: 要创建的目录路径
        parents: 是否创建父目录
        
    Returns:
        创建操作结果
    """
    try:
        if os.path.exists(dir_path):
            return {"success": False, "error": f"目录已存在: {dir_path}"}
        
        if parents:
            os.makedirs(dir_path)
        else:
            os.mkdir(dir_path)
            
        return {
            "success": True,
            "action": "directory_created",
            "path": dir_path,
            "parents": parents
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("复制文件或目录")
def copy_path(src_path: str, dst_path: str) -> Dict[str, Any]:
    """复制文件或目录
    
    Args:
        src_path: 源路径
        dst_path: 目标路径
        
    Returns:
        复制操作结果
    """
    try:
        if not os.path.exists(src_path):
            return {"success": False, "error": f"源路径不存在: {src_path}"}
        
        if os.path.isfile(src_path):
            shutil.copy2(src_path, dst_path)
            action = "file_copied"
        elif os.path.isdir(src_path):
            shutil.copytree(src_path, dst_path)
            action = "directory_copied"
        else:
            return {"success": False, "error": f"未知源路径类型: {src_path}"}
            
        return {
            "success": True,
            "action": action,
            "src_path": src_path,
            "dst_path": dst_path
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取文件信息")
def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件详细信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件信息字典
    """
    try:
        if not os.path.exists(file_path):
            return {"success": False, "error": f"文件不存在: {file_path}"}
        
        stat = os.stat(file_path)
        
        return {
            "success": True,
            "path": file_path,
            "name": os.path.basename(file_path),
            "size": stat.st_size,
            "is_file": os.path.isfile(file_path),
            "is_directory": os.path.isdir(file_path),
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
            "accessed_time": stat.st_atime,
            "permissions": oct(stat.st_mode)[-3:]
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("🗂️  启动文件操作工具服务器")
    print("📡 服务地址: http://localhost:8001")
    print("🔧 提供工具:")
    for tool_name in server.tools.keys():
        print(f"   - {tool_name}")
    print()
    
    server.run()
