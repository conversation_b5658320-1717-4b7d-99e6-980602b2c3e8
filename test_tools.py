#!/usr/bin/env python3
"""
测试工具注册和发现系统
"""
import sys
import os
import asyncio
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tool_registry import ToolRegistry
from qwen3_mcp_client import Configuration, Server

logging.basicConfig(level=logging.INFO)

async def test_tool_discovery():
    """测试工具发现功能"""
    print("🔍 测试工具发现功能...")
    
    # 创建工具注册器
    registry = ToolRegistry()
    
    # 扫描工具
    discovered_tools = registry.scan_tools()
    
    print(f"✅ 发现 {len(discovered_tools)} 个工具:")
    for tool_name, tool_info in discovered_tools.items():
        print(f"  - {tool_name}: {tool_info['description']}")
    
    # 更新配置
    registry.update_servers_config()
    print("✅ 配置文件已更新")
    
    return len(discovered_tools) > 0

async def test_server_initialization():
    """测试服务器初始化"""
    print("\n🚀 测试服务器初始化...")
    
    try:
        # 加载配置
        config = Configuration()
        server_config = config.load_config("servers_config.json")
        
        print(f"✅ 加载配置成功，发现 {len(server_config['mcpServers'])} 个服务器")
        
        # 创建服务器实例（但不启动）
        servers = [
            Server(name, srv_config)
            for name, srv_config in server_config["mcpServers"].items()
        ]
        
        print(f"✅ 创建 {len(servers)} 个服务器实例成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务器初始化失败: {e}")
        return False

async def test_qwen3_model():
    """测试 Qwen3 模型加载"""
    print("\n🤖 测试 Qwen3 模型加载...")
    
    try:
        from qwen3_test import QwenChatbot
        
        # 检查模型目录是否存在
        model_path = "./Qwen3-8B"
        if not os.path.exists(model_path):
            print(f"❌ 模型目录不存在: {model_path}")
            return False
            
        print("✅ 模型目录存在")
        print("ℹ️  注意：实际模型加载需要较长时间，这里只检查目录")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "qwen3_test.py",
        "qwen3_mcp_client.py", 
        "tool_registry.py",
        "start_qwen3_client.py",
        "mcp_server_open_file.py",
        "tools/__init__.py",
        "tools/file_operations.py",
        "tools/system_info.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

async def main():
    """运行所有测试"""
    print("🧪 开始系统测试...")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("工具发现", test_tool_discovery),
        ("服务器初始化", test_server_initialization),
        ("Qwen3模型", test_qwen3_model)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统准备就绪。")
        print("💡 运行 'python start_qwen3_client.py' 启动客户端")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    return passed == len(results)

if __name__ == "__main__":
    asyncio.run(main())
