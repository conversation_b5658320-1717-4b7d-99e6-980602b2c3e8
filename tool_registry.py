import os
import importlib.util
import inspect
import json
import logging
from typing import Dict, List, Any, Callable
from pathlib import Path

logger = logging.getLogger(__name__)

class ToolRegistry:
    """自动发现和注册工具类的注册系统"""

    def __init__(self, tools_dir: str = "tools"):
        self.tools_dir = Path(tools_dir)
        self.registered_tools: Dict[str, Dict[str, Any]] = {}
        self.tool_servers: Dict[str, Dict[str, Any]] = {}

    def scan_tools(self) -> Dict[str, Dict[str, Any]]:
        """扫描工具目录，自动发现工具类"""
        if not self.tools_dir.exists():
            logger.warning(f"Tools directory {self.tools_dir} does not exist")
            return {}

        discovered_tools = {}

        # 扫描工具目录中的所有Python文件
        for py_file in self.tools_dir.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue

            try:
                # 动态导入模块
                spec = importlib.util.spec_from_file_location(
                    py_file.stem, py_file
                )
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # 查找工具函数和类
                    tools = self._extract_tools_from_module(module, py_file)
                    if tools:
                        discovered_tools.update(tools)
                        logger.info(f"Discovered {len(tools)} tools in {py_file}")

            except Exception as e:
                logger.error(f"Error loading tools from {py_file}: {e}")

        self.registered_tools.update(discovered_tools)
        return discovered_tools

    def _extract_tools_from_module(self, module, file_path: Path) -> Dict[str, Dict[str, Any]]:
        """从模块中提取工具函数"""
        tools = {}

        # 查找带有 @tool 装饰器的函数
        for name, obj in inspect.getmembers(module):
            if inspect.isfunction(obj):
                # 检查是否有工具装饰器标记
                if hasattr(obj, '_is_mcp_tool'):
                    tool_info = self._create_tool_info(obj, file_path)
                    tools[name] = tool_info

            elif inspect.isclass(obj):
                # 检查类中的工具方法
                class_tools = self._extract_tools_from_class(obj, file_path)
                tools.update(class_tools)

        return tools

    def _extract_tools_from_class(self, cls, file_path: Path) -> Dict[str, Dict[str, Any]]:
        """从类中提取工具方法"""
        tools = {}

        # 检查类的所有方法（包括未绑定的方法）
        for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if hasattr(method, '_is_mcp_tool'):
                tool_name = f"{cls.__name__}.{name}"
                tool_info = self._create_tool_info(method, file_path)
                tools[tool_name] = tool_info

        return tools

    def _create_tool_info(self, func: Callable, file_path: Path) -> Dict[str, Any]:
        """创建工具信息字典"""
        # 获取函数签名和文档
        sig = inspect.signature(func)
        doc = inspect.getdoc(func) or "No description available"

        # 解析参数
        parameters = {}
        required = []

        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue

            param_info = {
                "type": self._get_param_type(param),
                "description": f"Parameter {param_name}"
            }

            parameters[param_name] = param_info

            if param.default == inspect.Parameter.empty:
                required.append(param_name)

        return {
            "name": func.__name__,
            "description": doc,
            "function": func,
            "file_path": str(file_path),
            "input_schema": {
                "type": "object",
                "properties": parameters,
                "required": required
            }
        }

    def _get_param_type(self, param: inspect.Parameter) -> str:
        """获取参数类型"""
        if param.annotation != inspect.Parameter.empty:
            if param.annotation == str:
                return "string"
            elif param.annotation == int:
                return "integer"
            elif param.annotation == float:
                return "number"
            elif param.annotation == bool:
                return "boolean"
            elif param.annotation == list:
                return "array"
            elif param.annotation == dict:
                return "object"
        return "string"  # 默认类型

    def generate_server_config(self) -> Dict[str, Any]:
        """生成MCP服务器配置"""
        config = {"mcpServers": {}}

        # 为每个工具文件生成服务器配置
        tool_files = set()
        for tool_info in self.registered_tools.values():
            tool_files.add(tool_info["file_path"])

        for file_path in tool_files:
            server_name = Path(file_path).stem
            config["mcpServers"][server_name] = {
                "command": "python",
                "args": [file_path],
                "env": {}
            }

        return config

    def update_servers_config(self, config_file: str = "servers_config.json"):
        """更新服务器配置文件"""
        try:
            # 读取现有配置
            existing_config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)

            # 生成新的工具服务器配置
            new_config = self.generate_server_config()

            # 合并配置
            if "mcpServers" not in existing_config:
                existing_config["mcpServers"] = {}

            existing_config["mcpServers"].update(new_config["mcpServers"])

            # 写入配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)

            logger.info(f"Updated server configuration: {config_file}")

        except Exception as e:
            logger.error(f"Error updating server config: {e}")

def tool(description: str = None):
    """工具装饰器，用于标记函数为MCP工具"""
    def decorator(func):
        func._is_mcp_tool = True
        func._tool_description = description or func.__doc__ or "No description"
        return func
    return decorator

# 全局工具注册实例
registry = ToolRegistry()
