#!/usr/bin/env python3
"""
网络化工具服务器基类
支持自动注册到 MCP Client 服务
"""
import asyncio
import json
import logging
import inspect
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Callable, Optional
from datetime import datetime

import httpx
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Pydantic 模型
class ToolCallRequest(BaseModel):
    tool_name: str
    arguments: Dict[str, Any]

class ToolCallResponse(BaseModel):
    success: bool
    result: Any
    error: Optional[str] = None
    execution_time: Optional[float] = None

class ToolInfo(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]

class ServerInfo(BaseModel):
    server_name: str
    server_url: str
    tools: List[ToolInfo]
    description: Optional[str] = None

class NetworkToolServer:
    """网络化工具服务器基类"""
    
    def __init__(self, 
                 server_name: str,
                 host: str = "0.0.0.0",
                 port: int = 8001,
                 client_registry_url: str = "http://localhost:8000/register-server",
                 description: str = None):
        self.server_name = server_name
        self.host = host
        self.port = port
        self.client_registry_url = client_registry_url
        self.description = description or f"{server_name} 工具服务器"
        
        self.tools: Dict[str, Callable] = {}
        self.tool_info: Dict[str, Dict[str, Any]] = {}
        
        # 创建 FastAPI 应用
        self.app = FastAPI(
            title=f"{server_name} Tool Server",
            description=self.description,
            version="1.0.0"
        )
        
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/")
        async def root():
            return {
                "server_name": self.server_name,
                "description": self.description,
                "tools_count": len(self.tools),
                "status": "running",
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/health")
        async def health():
            return {
                "status": "healthy",
                "server_name": self.server_name,
                "tools_count": len(self.tools)
            }
        
        @self.app.get("/tools")
        async def list_tools():
            """列出所有工具"""
            tools_list = []
            for tool_name, tool_info in self.tool_info.items():
                tools_list.append(ToolInfo(**tool_info))
            
            return {
                "server_name": self.server_name,
                "tools": tools_list,
                "count": len(tools_list)
            }
        
        @self.app.post("/call-tool", response_model=ToolCallResponse)
        async def call_tool(request: ToolCallRequest):
            """调用工具"""
            return await self._execute_tool(request.tool_name, request.arguments)
    
    def tool(self, description: str = None):
        """工具装饰器"""
        def decorator(func: Callable):
            tool_name = func.__name__
            tool_description = description or func.__doc__ or "无描述"
            
            # 注册工具
            self.tools[tool_name] = func
            
            # 解析工具信息
            tool_info = self._parse_tool_info(func, tool_description)
            self.tool_info[tool_name] = tool_info
            
            logger.info(f"注册工具: {tool_name}")
            return func
        
        return decorator
    
    def _parse_tool_info(self, func: Callable, description: str) -> Dict[str, Any]:
        """解析工具信息"""
        sig = inspect.signature(func)
        parameters = {}
        
        for param_name, param in sig.parameters.items():
            param_info = {
                "type": self._get_param_type(param),
                "description": f"参数 {param_name}",
                "required": param.default == inspect.Parameter.empty
            }
            parameters[param_name] = param_info
        
        return {
            "name": func.__name__,
            "description": description,
            "parameters": parameters
        }
    
    def _get_param_type(self, param: inspect.Parameter) -> str:
        """获取参数类型"""
        if param.annotation != inspect.Parameter.empty:
            if param.annotation == str:
                return "string"
            elif param.annotation == int:
                return "integer"
            elif param.annotation == float:
                return "number"
            elif param.annotation == bool:
                return "boolean"
            elif param.annotation == list:
                return "array"
            elif param.annotation == dict:
                return "object"
        return "string"
    
    async def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolCallResponse:
        """执行工具"""
        import time
        start_time = time.time()
        
        try:
            if tool_name not in self.tools:
                return ToolCallResponse(
                    success=False,
                    error=f"工具 {tool_name} 不存在"
                )
            
            tool_func = self.tools[tool_name]
            
            # 执行工具函数
            if asyncio.iscoroutinefunction(tool_func):
                result = await tool_func(**arguments)
            else:
                result = tool_func(**arguments)
            
            execution_time = time.time() - start_time
            
            return ToolCallResponse(
                success=True,
                result=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"工具 {tool_name} 执行失败: {e}")
            
            return ToolCallResponse(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def register_to_client(self):
        """注册到客户端"""
        try:
            server_url = f"http://{self.host}:{self.port}"
            
            # 构建工具信息
            tools_list = []
            for tool_info in self.tool_info.values():
                tools_list.append(tool_info)
            
            registration_data = {
                "server_name": self.server_name,
                "server_url": server_url,
                "tools": tools_list,
                "description": self.description
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.client_registry_url,
                    json=registration_data,
                    timeout=10.0
                )
                response.raise_for_status()
                result = response.json()
                
                logger.info(f"成功注册到客户端: {result}")
                return True
                
        except Exception as e:
            logger.error(f"注册到客户端失败: {e}")
            return False
    
    async def start_server(self, auto_register: bool = True):
        """启动服务器"""
        logger.info(f"启动工具服务器: {self.server_name}")
        logger.info(f"服务地址: http://{self.host}:{self.port}")
        logger.info(f"工具数量: {len(self.tools)}")
        
        if auto_register:
            # 延迟注册，等待服务器启动
            async def delayed_register():
                await asyncio.sleep(2)  # 等待服务器启动
                await self.register_to_client()
            
            asyncio.create_task(delayed_register())
        
        # 启动服务器
        config = uvicorn.Config(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    
    def run(self, auto_register: bool = True):
        """运行服务器"""
        try:
            asyncio.run(self.start_server(auto_register))
        except KeyboardInterrupt:
            logger.info("服务器已停止")

# 便捷函数
def create_tool_server(server_name: str, 
                      port: int = 8001,
                      client_url: str = "http://localhost:8000/register-server",
                      description: str = None) -> NetworkToolServer:
    """创建工具服务器实例"""
    return NetworkToolServer(
        server_name=server_name,
        port=port,
        client_registry_url=client_url,
        description=description
    )
