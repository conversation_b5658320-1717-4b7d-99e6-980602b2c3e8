# Qwen3 MCP Client 使用指南

## 🎯 项目概述

这个项目将您本地部署的 Qwen3 模型转换为一个强大的 MCP (Model Context Protocol) 客户端，支持自动工具发现和注册。通过这个系统，您可以：

- 🤖 使用本地 Qwen3 模型进行对话
- 🔧 自动发现和注册工具类代码
- 📦 即插即用的工具开发体验
- 💬 让 AI 根据需要自动调用合适的工具

## 🚀 快速开始

### 1. 检查系统
```bash
# 运行基础测试
python test_tools_basic.py

# 查看演示
python demo_standalone.py
```

### 2. 安装依赖
```bash
# 自动安装所有依赖
python install_dependencies.py

# 或手动安装
pip install modelscope torch transformers mcp python-docx psutil httpx python-dotenv
```

### 3. 确保模型存在
确保 `./Qwen3-8B/` 目录包含完整的 Qwen3 模型文件。

### 4. 启动客户端
```bash
python start_qwen3_client.py
```

## 💬 使用示例

启动后，您可以与 AI 进行自然对话，AI 会自动调用合适的工具：

```
你: 帮我查看当前目录的文件
助手: 我来帮您查看当前目录的文件...
[自动调用 list_directory 工具]

你: 查看系统的CPU使用情况
助手: 让我为您检查CPU使用情况...
[自动调用 get_cpu_info 工具]

你: 读取README文件的内容
助手: 我来为您读取README文件...
[自动调用 read_file 工具]
```

## 🛠️ 添加新工具

### 方法1: 在 tools/ 目录创建新工具文件

1. 创建新文件，例如 `tools/my_tools.py`
2. 编写工具代码：

```python
# tools/my_tools.py
from mcp.server.fastmcp import FastMCP
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tool_registry import tool

# 创建MCP服务器实例
mcp = FastMCP("my_tools")

@mcp.tool()
@tool("计算两个数的和")
def add_numbers(a: int, b: int) -> dict:
    """计算两个数的和
    
    Args:
        a: 第一个数
        b: 第二个数
        
    Returns:
        包含计算结果的字典
    """
    result = a + b
    return {
        "success": True,
        "result": result,
        "operation": f"{a} + {b} = {result}"
    }

@mcp.tool()
@tool("获取当前时间")
def get_current_time() -> dict:
    """获取当前时间
    
    Returns:
        包含当前时间的字典
    """
    import datetime
    now = datetime.datetime.now()
    return {
        "success": True,
        "current_time": now.isoformat(),
        "formatted_time": now.strftime("%Y-%m-%d %H:%M:%S")
    }

if __name__ == '__main__':
    mcp.run(transport="stdio")
```

3. 重启客户端，新工具会自动被发现和注册

### 方法2: 扩展现有工具文件

直接在 `tools/file_operations.py` 或 `tools/system_info.py` 中添加新函数。

## 📋 现有工具列表

### 文件操作工具 (file_operations)
- `read_file(file_path)` - 读取文件内容
- `write_file(file_path, content, mode)` - 写入文件内容
- `list_directory(dir_path, show_hidden)` - 列出目录内容
- `delete_path(path, recursive)` - 删除文件或目录

### 系统信息工具 (system_info)
- `get_system_info()` - 获取系统基本信息
- `get_cpu_info()` - 获取CPU使用信息
- `get_memory_info()` - 获取内存使用信息
- `get_disk_info()` - 获取磁盘使用信息
- `get_network_info()` - 获取网络接口信息
- `get_process_info(limit)` - 获取运行进程信息

### 文档操作工具 (open_file)
- `open_file()` - 打开并读取 Word 文档

## ⚙️ 配置说明

### servers_config.json
系统会自动生成和更新此配置文件：

```json
{
  "mcpServers": {
    "open_file": {
      "command": "python",
      "args": ["mcp_server_open_file.py"],
      "env": {}
    },
    "file_operations": {
      "command": "python",
      "args": ["tools/file_operations.py"],
      "env": {}
    },
    "system_info": {
      "command": "python",
      "args": ["tools/system_info.py"],
      "env": {}
    }
  }
}
```

## 🔧 工作原理

1. **工具发现**: 启动时扫描 `tools/` 目录中的 Python 文件
2. **装饰器识别**: 查找带有 `@tool` 装饰器的函数
3. **配置生成**: 自动更新 `servers_config.json`
4. **服务器启动**: 为每个工具文件启动 MCP 服务器
5. **模型集成**: Qwen3 模型了解所有可用工具
6. **智能调用**: AI 根据用户需求选择合适工具

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   - 确保 `Qwen3-8B/` 目录存在
   - 检查模型文件完整性
   - 确保有足够的内存和显存

2. **工具未发现**
   - 检查工具文件是否在 `tools/` 目录
   - 确保使用了 `@tool` 装饰器
   - 检查 Python 语法错误

3. **服务器启动失败**
   - 检查依赖是否正确安装
   - 查看错误日志
   - 确保端口未被占用

4. **工具调用失败**
   - 检查工具函数参数类型
   - 确保返回值格式正确
   - 查看服务器日志

### 调试技巧

```bash
# 检查工具发现
python -c "
from tool_registry import ToolRegistry
registry = ToolRegistry()
tools = registry.scan_tools()
print(f'发现 {len(tools)} 个工具')
"

# 测试单个工具服务器
python tools/file_operations.py

# 查看详细日志
python start_qwen3_client.py --verbose
```

## 🔮 扩展功能建议

- 📊 数据分析工具
- 🌐 网络请求工具
- 🗄️ 数据库操作工具
- 🖼️ 图像处理工具
- 📧 邮件发送工具
- 🔐 加密解密工具
- 📈 图表生成工具

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
