#!/usr/bin/env python3
"""
安装 Qwen3 MCP Client 所需的依赖
"""
import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败:")
        print(f"   错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return False

def install_dependencies():
    """安装依赖包"""
    dependencies = [
        # 基础依赖
        ("pip install --upgrade pip", "升级pip"),
        
        # MCP相关
        ("pip install mcp", "安装MCP"),
        
        # 模型相关
        ("pip install modelscope", "安装ModelScope"),
        ("pip install torch", "安装PyTorch"),
        ("pip install transformers", "安装Transformers"),
        
        # 工具相关
        ("pip install python-docx", "安装python-docx (文档处理)"),
        ("pip install psutil", "安装psutil (系统信息)"),
        
        # 其他工具
        ("pip install httpx", "安装httpx (HTTP客户端)"),
        ("pip install python-dotenv", "安装python-dotenv (环境变量)"),
    ]
    
    success_count = 0
    for command, description in dependencies:
        if run_command(command, description):
            success_count += 1
    
    return success_count, len(dependencies)

def check_model_directory():
    """检查模型目录"""
    print("\n🤖 检查模型目录...")
    model_path = "./Qwen3-8B"
    
    if not os.path.exists(model_path):
        print(f"⚠️  模型目录不存在: {model_path}")
        print("   请从以下地址下载Qwen3-8B模型:")
        print("   https://modelscope.cn/models/Qwen/Qwen3-8B")
        print("   或使用ModelScope下载:")
        print("   from modelscope import snapshot_download")
        print("   snapshot_download('Qwen/Qwen3-8B', cache_dir='./Qwen3-8B')")
        return False
    else:
        print(f"✅ 模型目录存在: {model_path}")
        return True

def main():
    """主函数"""
    print("🚀 Qwen3 MCP Client 依赖安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    print("\n📦 开始安装依赖...")
    success_count, total_count = install_dependencies()
    
    print(f"\n📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    # 检查模型目录
    model_exists = check_model_directory()
    
    print("\n" + "=" * 50)
    if success_count == total_count:
        print("🎉 所有依赖安装成功！")
        if model_exists:
            print("✅ 模型目录存在")
            print("🚀 现在可以运行: python start_qwen3_client.py")
        else:
            print("⚠️  请先下载Qwen3-8B模型")
    else:
        print("⚠️  部分依赖安装失败，请检查错误信息")
    
    print("\n💡 提示:")
    print("- 如果安装失败，可以尝试使用 pip install --user 安装到用户目录")
    print("- 如果网络问题，可以使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")

if __name__ == "__main__":
    main()
