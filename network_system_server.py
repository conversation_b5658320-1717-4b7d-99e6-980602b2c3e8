#!/usr/bin/env python3
"""
网络化系统信息工具服务器
"""
import os
import platform
import psutil
import datetime
from typing import Dict, Any

from network_tool_server import create_tool_server

# 创建工具服务器
server = create_tool_server(
    server_name="system_info",
    port=8002,
    description="系统信息工具服务器 - 提供CPU、内存、磁盘、网络等系统信息"
)

@server.tool("获取系统基本信息")
def get_system_info() -> Dict[str, Any]:
    """获取系统基本信息
    
    Returns:
        系统信息字典
    """
    try:
        return {
            "success": True,
            "system": platform.system(),
            "platform": platform.platform(),
            "architecture": platform.architecture(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "hostname": platform.node(),
            "current_time": datetime.datetime.now().isoformat(),
            "boot_time": datetime.datetime.fromtimestamp(psutil.boot_time()).isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取CPU信息")
def get_cpu_info() -> Dict[str, Any]:
    """获取CPU使用信息
    
    Returns:
        CPU信息字典
    """
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # 获取每个核心的使用率
        cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)
        
        return {
            "success": True,
            "cpu_percent": cpu_percent,
            "cpu_count_logical": cpu_count,
            "cpu_count_physical": psutil.cpu_count(logical=False),
            "cpu_per_core": cpu_per_core,
            "cpu_frequency": {
                "current": cpu_freq.current if cpu_freq else None,
                "min": cpu_freq.min if cpu_freq else None,
                "max": cpu_freq.max if cpu_freq else None
            },
            "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取内存信息")
def get_memory_info() -> Dict[str, Any]:
    """获取内存使用信息
    
    Returns:
        内存信息字典
    """
    try:
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        return {
            "success": True,
            "virtual_memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "free": memory.free,
                "percent": memory.percent,
                "total_gb": round(memory.total / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2)
            },
            "swap_memory": {
                "total": swap.total,
                "used": swap.used,
                "free": swap.free,
                "percent": swap.percent,
                "total_gb": round(swap.total / (1024**3), 2),
                "used_gb": round(swap.used / (1024**3), 2)
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取磁盘信息")
def get_disk_info() -> Dict[str, Any]:
    """获取磁盘使用信息
    
    Returns:
        磁盘信息字典
    """
    try:
        disk_partitions = []
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_partitions.append({
                    "device": partition.device,
                    "mountpoint": partition.mountpoint,
                    "fstype": partition.fstype,
                    "total": usage.total,
                    "used": usage.used,
                    "free": usage.free,
                    "percent": (usage.used / usage.total) * 100,
                    "total_gb": round(usage.total / (1024**3), 2),
                    "used_gb": round(usage.used / (1024**3), 2),
                    "free_gb": round(usage.free / (1024**3), 2)
                })
            except PermissionError:
                continue
        
        # 磁盘IO统计
        disk_io = psutil.disk_io_counters()
        
        return {
            "success": True,
            "disk_partitions": disk_partitions,
            "disk_io": {
                "read_count": disk_io.read_count,
                "write_count": disk_io.write_count,
                "read_bytes": disk_io.read_bytes,
                "write_bytes": disk_io.write_bytes,
                "read_time": disk_io.read_time,
                "write_time": disk_io.write_time
            } if disk_io else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取网络信息")
def get_network_info() -> Dict[str, Any]:
    """获取网络接口信息
    
    Returns:
        网络信息字典
    """
    try:
        network_interfaces = {}
        for interface, addresses in psutil.net_if_addrs().items():
            interface_info = []
            for addr in addresses:
                interface_info.append({
                    "family": str(addr.family),
                    "address": addr.address,
                    "netmask": addr.netmask,
                    "broadcast": addr.broadcast
                })
            network_interfaces[interface] = interface_info
        
        # 网络IO统计
        network_stats = psutil.net_io_counters()
        
        # 网络连接
        connections = len(psutil.net_connections())
        
        return {
            "success": True,
            "interfaces": network_interfaces,
            "io_counters": {
                "bytes_sent": network_stats.bytes_sent,
                "bytes_recv": network_stats.bytes_recv,
                "packets_sent": network_stats.packets_sent,
                "packets_recv": network_stats.packets_recv,
                "errin": network_stats.errin,
                "errout": network_stats.errout,
                "dropin": network_stats.dropin,
                "dropout": network_stats.dropout
            },
            "connections_count": connections
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取运行进程信息")
def get_process_info(limit: int = 10) -> Dict[str, Any]:
    """获取运行中的进程信息
    
    Args:
        limit: 返回进程数量限制
        
    Returns:
        进程信息字典
    """
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status', 'create_time']):
            try:
                proc_info = proc.info
                proc_info['create_time'] = datetime.datetime.fromtimestamp(proc_info['create_time']).isoformat()
                processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 按CPU使用率排序
        processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)
        
        return {
            "success": True,
            "processes": processes[:limit],
            "total_processes": len(processes),
            "limit": limit
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取系统温度信息")
def get_temperature_info() -> Dict[str, Any]:
    """获取系统温度信息
    
    Returns:
        温度信息字典
    """
    try:
        temperatures = {}
        
        # 尝试获取温度信息（Linux系统）
        if hasattr(psutil, 'sensors_temperatures'):
            temps = psutil.sensors_temperatures()
            for name, entries in temps.items():
                temp_list = []
                for entry in entries:
                    temp_list.append({
                        "label": entry.label or "Unknown",
                        "current": entry.current,
                        "high": entry.high,
                        "critical": entry.critical
                    })
                temperatures[name] = temp_list
        
        return {
            "success": True,
            "temperatures": temperatures,
            "available": len(temperatures) > 0
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@server.tool("获取电池信息")
def get_battery_info() -> Dict[str, Any]:
    """获取电池信息
    
    Returns:
        电池信息字典
    """
    try:
        battery = psutil.sensors_battery()
        
        if battery is None:
            return {
                "success": True,
                "battery_present": False,
                "message": "未检测到电池"
            }
        
        return {
            "success": True,
            "battery_present": True,
            "percent": battery.percent,
            "power_plugged": battery.power_plugged,
            "seconds_left": battery.secsleft if battery.secsleft != psutil.POWER_TIME_UNLIMITED else None,
            "time_left": str(datetime.timedelta(seconds=battery.secsleft)) if battery.secsleft not in [psutil.POWER_TIME_UNLIMITED, psutil.POWER_TIME_UNKNOWN] else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("💻 启动系统信息工具服务器")
    print("📡 服务地址: http://localhost:8002")
    print("🔧 提供工具:")
    for tool_name in server.tools.keys():
        print(f"   - {tool_name}")
    print()
    
    server.run()
