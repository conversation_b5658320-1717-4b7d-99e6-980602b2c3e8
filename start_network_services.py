#!/usr/bin/env python3
"""
网络服务启动管理器
启动 MCP Client 和所有工具服务器
"""
import asyncio
import subprocess
import sys
import time
import signal
import os
from typing import List, Dict, Any

import httpx

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.services = [
            {
                "name": "MCP Client API",
                "command": [sys.executable, "fastapi_mcp_client.py"],
                "port": 8000,
                "health_url": "http://localhost:8000/health"
            },
            {
                "name": "文件操作工具服务器",
                "command": [sys.executable, "network_file_server.py"],
                "port": 8001,
                "health_url": "http://localhost:8001/health"
            },
            {
                "name": "系统信息工具服务器",
                "command": [sys.executable, "network_system_server.py"],
                "port": 8002,
                "health_url": "http://localhost:8002/health"
            }
        ]
    
    def start_service(self, service: Dict[str, Any]) -> subprocess.Popen:
        """启动单个服务"""
        print(f"🚀 启动 {service['name']}...")
        
        try:
            process = subprocess.Popen(
                service["command"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print(f"✅ {service['name']} 已启动 (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"❌ 启动 {service['name']} 失败: {e}")
            return None
    
    async def wait_for_service(self, service: Dict[str, Any], timeout: int = 30) -> bool:
        """等待服务启动完成"""
        print(f"⏳ 等待 {service['name']} 启动完成...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(service["health_url"], timeout=5.0)
                    if response.status_code == 200:
                        print(f"✅ {service['name']} 启动完成")
                        return True
            except:
                pass
            
            await asyncio.sleep(1)
        
        print(f"❌ {service['name']} 启动超时")
        return False
    
    async def start_all_services(self):
        """启动所有服务"""
        print("🎯 启动网络化 MCP 服务集群")
        print("=" * 50)
        
        # 启动所有服务
        for service in self.services:
            process = self.start_service(service)
            if process:
                self.processes.append(process)
                # 短暂延迟，避免端口冲突
                await asyncio.sleep(2)
        
        print(f"\n⏳ 等待所有服务启动完成...")
        
        # 等待所有服务启动完成
        all_started = True
        for service in self.services:
            if not await self.wait_for_service(service):
                all_started = False
        
        if all_started:
            print("\n🎉 所有服务启动成功！")
            self.print_service_info()
            return True
        else:
            print("\n❌ 部分服务启动失败")
            return False
    
    def print_service_info(self):
        """打印服务信息"""
        print("\n📡 服务信息:")
        print("-" * 40)
        for service in self.services:
            print(f"🔗 {service['name']}")
            print(f"   地址: http://localhost:{service['port']}")
            if service['port'] == 8000:
                print(f"   API文档: http://localhost:{service['port']}/docs")
        
        print("\n💡 使用方法:")
        print("1. 访问 http://localhost:8000/docs 查看 API 文档")
        print("2. 运行 'python test_api_client.py' 进行测试")
        print("3. 使用 HTTP 客户端调用 /chat 接口进行对话")
        print("\n按 Ctrl+C 停止所有服务")
    
    def stop_all_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止所有服务...")
        
        for i, process in enumerate(self.processes):
            if process and process.poll() is None:
                service_name = self.services[i]["name"]
                print(f"⏹️  停止 {service_name}...")
                
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"⚠️  强制终止 {service_name}")
                    process.kill()
                except Exception as e:
                    print(f"❌ 停止 {service_name} 失败: {e}")
        
        print("✅ 所有服务已停止")
    
    async def monitor_services(self):
        """监控服务状态"""
        try:
            while True:
                # 检查进程状态
                for i, process in enumerate(self.processes):
                    if process and process.poll() is not None:
                        service_name = self.services[i]["name"]
                        print(f"⚠️  {service_name} 意外退出")
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            pass
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止服务...")
        self.stop_all_services()
        sys.exit(0)

async def main():
    """主函数"""
    manager = ServiceManager()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        # 启动所有服务
        if await manager.start_all_services():
            # 监控服务状态
            await manager.monitor_services()
        else:
            print("❌ 服务启动失败，退出")
            manager.stop_all_services()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止服务...")
    except Exception as e:
        print(f"❌ 服务管理器异常: {e}")
    finally:
        manager.stop_all_services()

if __name__ == "__main__":
    print("🌐 网络化 MCP 服务管理器")
    print("=" * 50)
    print("这将启动以下服务:")
    print("1. FastAPI MCP Client (端口 8000)")
    print("2. 文件操作工具服务器 (端口 8001)")
    print("3. 系统信息工具服务器 (端口 8002)")
    print()
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
        import httpx
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install fastapi uvicorn httpx")
        sys.exit(1)
    
    print()
    asyncio.run(main())
