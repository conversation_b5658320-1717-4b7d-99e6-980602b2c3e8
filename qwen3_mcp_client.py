import asyncio
import json
import logging
import os
import shutil
from contextlib import AsyncExitStack
from typing import Any, List, Dict

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from qwen3_test import Qwen<PERSON><PERSON>bot
from tool_registry import ToolRegistry

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

class Configuration:
    """管理MCP客户端的配置"""

    def __init__(self) -> None:
        """初始化配置"""
        pass

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """从JSON文件加载服务器配置

        Args:
            file_path: JSON配置文件路径

        Returns:
            包含服务器配置的字典

        Raises:
            FileNotFoundError: 如果配置文件不存在
            JSONDecodeError: 如果配置文件不是有效的JSON
        """
        with open(file_path, "r", encoding='utf-8') as f:
            return json.load(f)

class Server:
    """管理MCP服务器连接和工具执行"""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()

    async def initialize(self) -> None:
        """初始化服务器连接"""
        command = (
            shutil.which("python")
            if self.config["command"] == "python"
            else self.config["command"]
        )
        if command is None:
            raise ValueError("The command must be a valid string and cannot be None.")

        server_params = StdioServerParameters(
            command=command,
            args=self.config["args"],
            env={**os.environ, **self.config["env"]}
            if self.config.get("env")
            else None,
        )
        try:
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            read, write = stdio_transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read, write)
            )
            await session.initialize()
            self.session = session
        except Exception as e:
            logging.error(f"Error initializing server {self.name}: {e}")
            await self.cleanup()
            raise

    async def list_tools(self) -> list[Any]:
        """列出服务器可用的工具

        Returns:
            可用工具列表

        Raises:
            RuntimeError: 如果服务器未初始化
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        tools_response = await self.session.list_tools()
        tools = []

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools.extend(
                    Tool(tool.name, tool.description, tool.inputSchema, tool.title)
                    for tool in item[1]
                )

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """执行工具，带重试机制

        Args:
            tool_name: 要执行的工具名称
            arguments: 工具参数
            retries: 重试次数
            delay: 重试间隔（秒）

        Returns:
            工具执行结果

        Raises:
            RuntimeError: 如果服务器未初始化
            Exception: 如果所有重试后工具执行仍失败
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        attempt = 0
        while attempt < retries:
            try:
                logging.info(f"Executing {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments)
                return result

            except Exception as e:
                attempt += 1
                logging.warning(
                    f"Error executing tool: {e}. Attempt {attempt} of {retries}."
                )
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logging.error("Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """清理服务器资源"""
        async with self._cleanup_lock:
            try:
                await self.exit_stack.aclose()
                self.session = None
                self.stdio_context = None
            except Exception as e:
                logging.error(f"Error during cleanup of server {self.name}: {e}")

class Tool:
    """表示工具及其属性和格式化"""

    def __init__(
        self,
        name: str,
        description: str,
        input_schema: dict[str, Any],
        title: str | None = None,
    ) -> None:
        self.name: str = name
        self.title: str | None = title
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """为LLM格式化工具信息

        Returns:
            描述工具的格式化字符串
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        # 构建格式化输出，标题作为单独字段
        output = f"Tool: {self.name}\n"

        # 如果有可读标题则添加
        if self.title:
            output += f"User-readable title: {self.title}\n"

        output += f"""Description: {self.description}
Arguments:
{chr(10).join(args_desc)}
"""

        return output

class Qwen3LLMClient:
    """管理与Qwen3模型的通信"""

    def __init__(self, model_name: str = "./Qwen3-8B") -> None:
        self.chatbot = QwenChatbot(model_name)
        logging.info(f"Initialized Qwen3 model: {model_name}")

    def get_response(self, messages: List[Dict[str, str]]) -> str:
        """从Qwen3模型获取响应

        Args:
            messages: 消息字典列表

        Returns:
            LLM的响应字符串
        """
        try:
            response = self.chatbot.get_response_for_messages(messages)
            return response
        except Exception as e:
            error_message = f"Error getting Qwen3 response: {str(e)}"
            logging.error(error_message)
            return (
                f"I encountered an error: {error_message}. "
                "Please try again or rephrase your request."
            )

class ChatSession:
    """协调用户、LLM和工具之间的交互"""

    def __init__(self, servers: list[Server], llm_client: Qwen3LLMClient) -> None:
        self.servers: list[Server] = servers
        self.llm_client: Qwen3LLMClient = llm_client

    async def cleanup_servers(self) -> None:
        """正确清理所有服务器"""
        for server in reversed(self.servers):
            try:
                await server.cleanup()
            except Exception as e:
                logging.warning(f"Warning during final cleanup: {e}")

    async def process_llm_response(self, llm_response: str) -> str:
        """处理LLM响应并在需要时执行工具

        Args:
            llm_response: 来自LLM的响应

        Returns:
            工具执行结果或原始响应
        """
        try:
            tool_call = json.loads(llm_response)
            if "tool" in tool_call and "arguments" in tool_call:
                logging.info(f"Executing tool: {tool_call['tool']}")
                logging.info(f"With arguments: {tool_call['arguments']}")

                for server in self.servers:
                    tools = await server.list_tools()
                    if any(tool.name == tool_call["tool"] for tool in tools):
                        try:
                            result = await server.execute_tool(
                                tool_call["tool"], tool_call["arguments"]
                            )

                            if isinstance(result, dict) and "progress" in result:
                                progress = result["progress"]
                                total = result["total"]
                                percentage = (progress / total) * 100
                                logging.info(
                                    f"Progress: {progress}/{total} ({percentage:.1f}%)"
                                )

                            return f"Tool execution result: {result}"
                        except Exception as e:
                            error_msg = f"Error executing tool: {str(e)}"
                            logging.error(error_msg)
                            return error_msg

                return f"No server found with tool: {tool_call['tool']}"
            return llm_response
        except json.JSONDecodeError:
            return llm_response

    async def start(self) -> None:
        """主聊天会话处理器"""
        try:
            # 初始化所有服务器
            for server in self.servers:
                try:
                    await server.initialize()
                except Exception as e:
                    logging.error(f"Failed to initialize server: {e}")
                    await self.cleanup_servers()
                    return

            # 收集所有工具
            all_tools = []
            for server in self.servers:
                tools = await server.list_tools()
                all_tools.extend(tools)

            tools_description = "\n".join([tool.format_for_llm() for tool in all_tools])

            system_message = (
                "你是一个有用的助手，可以使用以下工具:\n\n"
                f"{tools_description}\n"
                "根据用户的问题选择合适的工具。"
                "如果不需要工具，请直接回复。\n\n"
                "重要提示：当你需要使用工具时，你必须只回复下面的确切JSON对象格式，不要添加其他内容:\n"
                "{\n"
                '    "tool": "工具名称",\n'
                '    "arguments": {\n'
                '        "参数名": "值"\n'
                "    }\n"
                "}\n\n"
                "收到工具响应后:\n"
                "1. 将原始数据转换为自然、对话式的响应\n"
                "2. 保持响应简洁但信息丰富\n"
                "3. 专注于最相关的信息\n"
                "4. 使用用户问题中的适当上下文\n"
                "5. 避免简单重复原始数据\n\n"
                "请只使用上面明确定义的工具。"
            )

            messages = [{"role": "system", "content": system_message}]

            print("Qwen3 MCP Client 已启动！输入 'quit' 或 'exit' 退出。")
            print("=" * 50)

            while True:
                try:
                    user_input = input("\n你: ").strip()
                    if user_input.lower() in ["quit", "exit", "退出"]:
                        logging.info("\n正在退出...")
                        break

                    messages.append({"role": "user", "content": user_input})

                    llm_response = self.llm_client.get_response(messages)
                    print(f"\n助手: {llm_response}")

                    result = await self.process_llm_response(llm_response)

                    if result != llm_response:
                        messages.append({"role": "assistant", "content": llm_response})
                        messages.append({"role": "system", "content": result})

                        final_response = self.llm_client.get_response(messages)
                        print(f"\n最终回复: {final_response}")
                        messages.append(
                            {"role": "assistant", "content": final_response}
                        )
                    else:
                        messages.append({"role": "assistant", "content": llm_response})

                except KeyboardInterrupt:
                    logging.info("\n正在退出...")
                    break

        finally:
            await self.cleanup_servers()

async def main() -> None:
    """初始化并运行聊天会话"""
    # 初始化工具注册系统
    tool_registry = ToolRegistry()

    # 扫描并注册工具
    logging.info("Scanning for tools...")
    discovered_tools = tool_registry.scan_tools()
    logging.info(f"Discovered {len(discovered_tools)} tools")

    # 更新服务器配置
    tool_registry.update_servers_config()

    # 加载配置
    config = Configuration()
    server_config = config.load_config("servers_config.json")
    servers = [
        Server(name, srv_config)
        for name, srv_config in server_config["mcpServers"].items()
    ]

    # 创建Qwen3客户端
    llm_client = Qwen3LLMClient()

    # 启动聊天会话
    chat_session = ChatSession(servers, llm_client)
    await chat_session.start()

if __name__ == "__main__":
    asyncio.run(main())
