<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qwen3 MCP Client Web Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 0;
            min-height: 600px;
        }
        
        .chat-area {
            padding: 30px;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-left: 1px solid #e9ecef;
        }
        
        .chat-messages {
            flex: 1;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
        }
        
        .message.assistant {
            background: #e9ecef;
            color: #333;
        }
        
        .message.system {
            background: #fff3cd;
            color: #856404;
            font-size: 0.9em;
            max-width: 100%;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
        }
        
        .input-area input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
        }
        
        .input-area input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .input-area button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .input-area button:hover {
            background: #0056b3;
        }
        
        .input-area button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.online {
            background: #28a745;
        }
        
        .tools-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .tool-item {
            padding: 8px 12px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            font-size: 0.9em;
            border: 1px solid #e9ecef;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                border-left: none;
                border-top: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Qwen3 MCP Client</h1>
            <p>基于网络的智能助手，支持动态工具调用</p>
        </div>
        
        <div class="main-content">
            <div class="chat-area">
                <div class="chat-messages" id="chatMessages">
                    <div class="message system">
                        欢迎使用 Qwen3 MCP Client！我可以帮您执行各种任务，包括文件操作、系统信息查询等。
                    </div>
                </div>
                
                <div class="input-area">
                    <input type="text" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
                    <button onclick="sendMessage()" id="sendButton">发送</button>
                </div>
            </div>
            
            <div class="sidebar">
                <h3>🔗 服务状态</h3>
                <div class="status-item">
                    <span>MCP Client</span>
                    <div class="status-indicator" id="clientStatus"></div>
                </div>
                <div class="status-item">
                    <span>工具服务器</span>
                    <div class="status-indicator" id="toolsStatus"></div>
                </div>
                
                <h3 style="margin-top: 30px;">🛠️ 可用工具</h3>
                <div class="tools-list" id="toolsList">
                    <div class="tool-item">加载中...</div>
                </div>
                
                <h3 style="margin-top: 30px;">💡 示例问题</h3>
                <div class="tool-item" onclick="setMessage('查看系统信息')" style="cursor: pointer;">
                    查看系统信息
                </div>
                <div class="tool-item" onclick="setMessage('列出当前目录文件')" style="cursor: pointer;">
                    列出当前目录文件
                </div>
                <div class="tool-item" onclick="setMessage('检查CPU使用情况')" style="cursor: pointer;">
                    检查CPU使用情况
                </div>
                <div class="tool-item" onclick="setMessage('查看内存使用情况')" style="cursor: pointer;">
                    查看内存使用情况
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 页面加载时初始化
        window.onload = function() {
            checkServiceStatus();
            loadTools();
            setInterval(checkServiceStatus, 10000); // 每10秒检查一次状态
        };
        
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    document.getElementById('clientStatus').classList.add('online');
                    
                    // 检查工具服务器
                    const serversResponse = await fetch(`${API_BASE}/servers`);
                    if (serversResponse.ok) {
                        const data = await serversResponse.json();
                        if (data.count > 0) {
                            document.getElementById('toolsStatus').classList.add('online');
                        }
                    }
                }
            } catch (error) {
                document.getElementById('clientStatus').classList.remove('online');
                document.getElementById('toolsStatus').classList.remove('online');
            }
        }
        
        // 加载工具列表
        async function loadTools() {
            try {
                const response = await fetch(`${API_BASE}/tools`);
                if (response.ok) {
                    const data = await response.json();
                    const toolsList = document.getElementById('toolsList');
                    toolsList.innerHTML = '';
                    
                    data.tools.forEach(tool => {
                        const toolItem = document.createElement('div');
                        toolItem.className = 'tool-item';
                        toolItem.innerHTML = `
                            <strong>${tool.tool_name}</strong><br>
                            <small>${tool.server_name}</small>
                        `;
                        toolsList.appendChild(toolItem);
                    });
                    
                    if (data.tools.length === 0) {
                        toolsList.innerHTML = '<div class="tool-item">暂无可用工具</div>';
                    }
                }
            } catch (error) {
                console.error('加载工具失败:', error);
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';
            
            // 禁用发送按钮
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            sendButton.innerHTML = '<div class="loading"></div>';
            
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        use_tools: true
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addMessage(data.response, 'assistant');
                    
                    if (data.tool_calls && data.tool_calls.length > 0) {
                        const toolInfo = data.tool_calls.map(call => 
                            `调用了工具: ${call.tool_name} (${call.server_name})`
                        ).join(', ');
                        addMessage(toolInfo, 'system');
                    }
                } else {
                    addMessage('抱歉，服务暂时不可用。', 'assistant');
                }
            } catch (error) {
                addMessage('网络错误，请检查服务是否正常运行。', 'assistant');
            } finally {
                // 恢复发送按钮
                sendButton.disabled = false;
                sendButton.innerHTML = '发送';
            }
        }
        
        // 添加消息到聊天区域
        function addMessage(text, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 设置消息内容
        function setMessage(text) {
            document.getElementById('messageInput').value = text;
        }
    </script>
</body>
</html>
