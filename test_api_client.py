#!/usr/bin/env python3
"""
API 客户端测试脚本
测试 FastAPI MCP Client 的各种接口
"""
import asyncio
import json
import time
from typing import Dict, Any

import httpx

class MCPClientTester:
    """MCP 客户端测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def test_health(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = await self.client.get(f"{self.base_url}/health")
            response.raise_for_status()
            result = response.json()
            print(f"✅ 健康检查通过: {result}")
            return True
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    async def test_list_servers(self):
        """测试列出服务器"""
        print("\n📋 测试列出已注册服务器...")
        try:
            response = await self.client.get(f"{self.base_url}/servers")
            response.raise_for_status()
            result = response.json()
            print(f"✅ 已注册服务器数量: {result['count']}")
            for server_name, server_info in result['servers'].items():
                print(f"   - {server_name}: {len(server_info['tools'])} 个工具")
            return True
        except Exception as e:
            print(f"❌ 列出服务器失败: {e}")
            return False
    
    async def test_list_tools(self):
        """测试列出工具"""
        print("\n🔧 测试列出所有工具...")
        try:
            response = await self.client.get(f"{self.base_url}/tools")
            response.raise_for_status()
            result = response.json()
            print(f"✅ 可用工具数量: {result['count']}")
            for tool in result['tools'][:5]:  # 只显示前5个
                print(f"   - {tool['tool_name']} ({tool['server_name']}): {tool['description'][:50]}...")
            if result['count'] > 5:
                print(f"   ... 还有 {result['count'] - 5} 个工具")
            return True
        except Exception as e:
            print(f"❌ 列出工具失败: {e}")
            return False
    
    async def test_chat(self, message: str, use_tools: bool = True):
        """测试聊天接口"""
        print(f"\n💬 测试聊天: \"{message}\"")
        try:
            chat_data = {
                "message": message,
                "use_tools": use_tools
            }
            
            response = await self.client.post(
                f"{self.base_url}/chat",
                json=chat_data
            )
            response.raise_for_status()
            result = response.json()
            
            print(f"✅ AI 回复: {result['response']}")
            if result['tool_calls']:
                print(f"🔧 调用了 {len(result['tool_calls'])} 个工具")
                for tool_call in result['tool_calls']:
                    print(f"   - {tool_call.get('tool_name', 'unknown')} ({tool_call.get('server_name', 'unknown')})")
            
            return True
        except Exception as e:
            print(f"❌ 聊天失败: {e}")
            return False
    
    async def test_direct_tool_call(self):
        """测试直接工具调用"""
        print("\n🔧 测试直接工具调用...")
        try:
            tool_data = {
                "tool_name": "get_system_info",
                "server_name": "system_info",
                "arguments": {}
            }
            
            response = await self.client.post(
                f"{self.base_url}/call-tool",
                json=tool_data
            )
            response.raise_for_status()
            result = response.json()
            
            if result['success']:
                print("✅ 工具调用成功")
                system_info = result['result']
                if isinstance(system_info, dict) and 'system' in system_info:
                    print(f"   系统: {system_info.get('system', 'Unknown')}")
                    print(f"   平台: {system_info.get('platform', 'Unknown')}")
            else:
                print(f"❌ 工具调用失败: {result.get('error', 'Unknown error')}")
            
            return result['success']
        except Exception as e:
            print(f"❌ 直接工具调用失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始 MCP Client API 测试")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.test_health),
            ("列出服务器", self.test_list_servers),
            ("列出工具", self.test_list_tools),
            ("直接工具调用", self.test_direct_tool_call),
        ]
        
        # 基础测试
        results = []
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                results.append((test_name, False))
        
        # 聊天测试
        chat_tests = [
            ("查看系统信息", True),
            ("列出当前目录文件", True),
            ("你好，请介绍一下自己", False),  # 不使用工具
        ]
        
        for message, use_tools in chat_tests:
            try:
                result = await self.test_chat(message, use_tools)
                results.append((f"聊天: {message}", result))
            except Exception as e:
                print(f"❌ 聊天测试异常: {e}")
                results.append((f"聊天: {message}", False))
        
        # 结果汇总
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(results)} 个测试通过")
        
        if passed == len(results):
            print("🎉 所有测试通过！")
        else:
            print("⚠️  部分测试失败")
        
        return passed == len(results)
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()

async def main():
    """主函数"""
    print("🚀 MCP Client API 测试工具")
    print("请确保以下服务正在运行:")
    print("1. FastAPI MCP Client (http://localhost:8000)")
    print("2. 文件操作工具服务器 (http://localhost:8001)")
    print("3. 系统信息工具服务器 (http://localhost:8002)")
    print()
    
    # 等待用户确认
    input("按 Enter 键开始测试...")
    
    tester = MCPClientTester()
    
    try:
        await tester.run_all_tests()
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
