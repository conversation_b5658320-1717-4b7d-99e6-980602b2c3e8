#!/usr/bin/env python3
"""
基于 FastAPI 的 MCP Client 服务
支持网络化工具服务器自动注册和 HTTP API 接口
"""
import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

import httpx
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from qwen3_test import QwenChatbot

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 全局变量
qwen_client: Optional[QwenChatbot] = None
registered_servers: Dict[str, Dict[str, Any]] = {}
client_config: Dict[str, Any] = {}

# Pydantic 模型
class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    use_tools: bool = True

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    tool_calls: List[Dict[str, Any]] = []
    timestamp: str

class ServerRegistration(BaseModel):
    server_name: str
    server_url: str
    tools: List[Dict[str, Any]]
    description: Optional[str] = None

class ToolCall(BaseModel):
    tool_name: str
    server_name: str
    arguments: Dict[str, Any]

class ToolResponse(BaseModel):
    success: bool
    result: Any
    error: Optional[str] = None

# 启动时初始化
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    await initialize_client()
    yield
    # 关闭时清理
    await cleanup_client()

# 创建 FastAPI 应用
app = FastAPI(
    title="Qwen3 MCP Client API",
    description="基于 Qwen3 的 MCP 客户端服务，支持动态工具注册",
    version="1.0.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

async def initialize_client():
    """初始化客户端"""
    global qwen_client, client_config
    
    try:
        # 加载配置
        with open("client_config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
            client_config = config["client_config"]
        
        logger.info("正在初始化 Qwen3 模型...")
        qwen_client = QwenChatbot(client_config["model_path"])
        logger.info("Qwen3 模型初始化完成")
        
    except Exception as e:
        logger.error(f"客户端初始化失败: {e}")
        raise

async def cleanup_client():
    """清理客户端资源"""
    logger.info("正在清理客户端资源...")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Qwen3 MCP Client API",
        "version": "1.0.0",
        "status": "running",
        "registered_servers": len(registered_servers),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "model_loaded": qwen_client is not None,
        "registered_servers": len(registered_servers),
        "timestamp": datetime.now().isoformat()
    }

@app.post("/register-server")
async def register_server(registration: ServerRegistration):
    """注册工具服务器"""
    global registered_servers
    
    try:
        server_info = {
            "server_name": registration.server_name,
            "server_url": registration.server_url,
            "tools": registration.tools,
            "description": registration.description,
            "registered_at": datetime.now().isoformat(),
            "status": "active"
        }
        
        registered_servers[registration.server_name] = server_info
        
        # 保存到配置文件
        await save_server_registry()
        
        logger.info(f"服务器 {registration.server_name} 注册成功，包含 {len(registration.tools)} 个工具")
        
        return {
            "success": True,
            "message": f"服务器 {registration.server_name} 注册成功",
            "tools_count": len(registration.tools)
        }
        
    except Exception as e:
        logger.error(f"服务器注册失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器注册失败: {str(e)}")

@app.get("/servers")
async def list_servers():
    """列出已注册的服务器"""
    return {
        "servers": registered_servers,
        "count": len(registered_servers)
    }

@app.get("/tools")
async def list_tools():
    """列出所有可用工具"""
    all_tools = []
    
    for server_name, server_info in registered_servers.items():
        for tool in server_info["tools"]:
            tool_info = {
                "tool_name": tool["name"],
                "server_name": server_name,
                "server_url": server_info["server_url"],
                "description": tool.get("description", ""),
                "parameters": tool.get("parameters", {})
            }
            all_tools.append(tool_info)
    
    return {
        "tools": all_tools,
        "count": len(all_tools)
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口"""
    if not qwen_client:
        raise HTTPException(status_code=503, detail="模型未初始化")
    
    try:
        conversation_id = request.conversation_id or f"conv_{int(time.time())}"
        
        # 构建系统消息
        system_message = await build_system_message() if request.use_tools else "你是一个有用的AI助手。"
        
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": request.message}
        ]
        
        # 获取模型响应
        response = qwen_client.get_response_for_messages(messages)
        
        tool_calls = []
        final_response = response
        
        # 检查是否需要调用工具
        if request.use_tools:
            tool_call_result = await process_tool_calls(response)
            if tool_call_result:
                tool_calls = tool_call_result["tool_calls"]
                final_response = tool_call_result["final_response"]
        
        return ChatResponse(
            response=final_response,
            conversation_id=conversation_id,
            tool_calls=tool_calls,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"聊天处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"聊天处理失败: {str(e)}")

@app.post("/call-tool")
async def call_tool(tool_call: ToolCall):
    """直接调用工具"""
    try:
        if tool_call.server_name not in registered_servers:
            raise HTTPException(status_code=404, detail=f"服务器 {tool_call.server_name} 未找到")
        
        server_info = registered_servers[tool_call.server_name]
        server_url = server_info["server_url"]
        
        # 调用远程工具
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{server_url}/call-tool",
                json={
                    "tool_name": tool_call.tool_name,
                    "arguments": tool_call.arguments
                },
                timeout=30.0
            )
            response.raise_for_status()
            result = response.json()
        
        return ToolResponse(
            success=True,
            result=result
        )
        
    except Exception as e:
        logger.error(f"工具调用失败: {e}")
        return ToolResponse(
            success=False,
            error=str(e)
        )

async def build_system_message() -> str:
    """构建包含工具信息的系统消息"""
    if not registered_servers:
        return "你是一个有用的AI助手。"
    
    tools_description = []
    for server_name, server_info in registered_servers.items():
        for tool in server_info["tools"]:
            tool_desc = f"工具: {tool['name']}\n"
            tool_desc += f"服务器: {server_name}\n"
            tool_desc += f"描述: {tool.get('description', '无描述')}\n"
            
            if "parameters" in tool:
                params = tool["parameters"]
                if params:
                    tool_desc += "参数:\n"
                    for param_name, param_info in params.items():
                        tool_desc += f"  - {param_name}: {param_info.get('description', '无描述')}"
                        if param_info.get('required', False):
                            tool_desc += " (必需)"
                        tool_desc += "\n"
            
            tools_description.append(tool_desc)
    
    system_message = (
        "你是一个有用的AI助手，可以使用以下工具:\n\n"
        + "\n".join(tools_description) +
        "\n\n当你需要使用工具时，请返回以下JSON格式:\n"
        "{\n"
        '  "tool_name": "工具名称",\n'
        '  "server_name": "服务器名称",\n'
        '  "arguments": {"参数名": "参数值"}\n'
        "}\n\n"
        "如果不需要工具，请直接回复用户。"
    )
    
    return system_message

async def process_tool_calls(response: str) -> Optional[Dict[str, Any]]:
    """处理工具调用"""
    try:
        # 尝试解析JSON
        tool_call_data = json.loads(response)
        
        if not all(key in tool_call_data for key in ["tool_name", "server_name", "arguments"]):
            return None
        
        # 执行工具调用
        tool_call = ToolCall(**tool_call_data)
        tool_result = await call_tool(tool_call)
        
        if tool_result.success:
            # 生成最终响应
            messages = [
                {"role": "system", "content": "请根据工具执行结果，用自然语言回复用户。"},
                {"role": "user", "content": f"工具执行结果: {json.dumps(tool_result.result, ensure_ascii=False)}"}
            ]
            
            final_response = qwen_client.get_response_for_messages(messages)
            
            return {
                "tool_calls": [tool_call_data],
                "final_response": final_response
            }
        else:
            return {
                "tool_calls": [tool_call_data],
                "final_response": f"工具调用失败: {tool_result.error}"
            }
            
    except json.JSONDecodeError:
        return None
    except Exception as e:
        logger.error(f"工具调用处理失败: {e}")
        return None

async def save_server_registry():
    """保存服务器注册信息"""
    try:
        config_data = {
            "client_config": client_config,
            "registered_servers": registered_servers,
            "server_registry_endpoint": f"http://localhost:{client_config['port']}/register-server"
        }
        
        with open("client_config.json", "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        logger.error(f"保存服务器注册信息失败: {e}")

if __name__ == "__main__":
    import uvicorn
    
    # 加载配置
    try:
        with open("client_config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
            host = config["client_config"]["host"]
            port = config["client_config"]["port"]
    except:
        host = "0.0.0.0"
        port = 8000
    
    print(f"🚀 启动 Qwen3 MCP Client API 服务")
    print(f"📡 服务地址: http://{host}:{port}")
    print(f"📚 API 文档: http://{host}:{port}/docs")
    
    uvicorn.run(app, host=host, port=port)
