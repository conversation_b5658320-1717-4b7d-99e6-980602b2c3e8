#!/usr/bin/env python3
"""
基础测试脚本 - 不依赖模型库
"""
import sys
import os
import json
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO)

def test_file_structure():
    """测试文件结构"""
    print("📁 测试文件结构...")
    
    required_files = [
        "qwen3_test.py",
        "qwen3_mcp_client.py", 
        "tool_registry.py",
        "start_qwen3_client.py",
        "mcp_server_open_file.py",
        "tools/__init__.py",
        "tools/file_operations.py",
        "tools/system_info.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def test_tool_registry_import():
    """测试工具注册系统导入"""
    print("\n🔧 测试工具注册系统...")
    
    try:
        from tool_registry import ToolRegistry, tool
        print("✅ 工具注册系统导入成功")
        
        # 测试装饰器
        @tool("测试工具")
        def test_function():
            return "test"
            
        if hasattr(test_function, '_is_mcp_tool'):
            print("✅ 工具装饰器工作正常")
        else:
            print("❌ 工具装饰器未正常工作")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 工具注册系统导入失败: {e}")
        return False

def test_config_file():
    """测试配置文件"""
    print("\n⚙️  测试配置文件...")
    
    config_file = "servers_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        if "mcpServers" not in config:
            print("❌ 配置文件格式错误：缺少 mcpServers")
            return False
            
        print(f"✅ 配置文件格式正确，包含 {len(config['mcpServers'])} 个服务器")
        
        for server_name, server_config in config['mcpServers'].items():
            print(f"  - {server_name}: {server_config.get('command', 'unknown')} {' '.join(server_config.get('args', []))}")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_tool_files():
    """测试工具文件语法"""
    print("\n🛠️  测试工具文件语法...")
    
    tool_files = [
        "tools/file_operations.py",
        "tools/system_info.py"
    ]
    
    results = []
    for tool_file in tool_files:
        try:
            with open(tool_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 简单的语法检查
            compile(content, tool_file, 'exec')
            print(f"✅ {tool_file} 语法正确")
            results.append(True)
            
        except SyntaxError as e:
            print(f"❌ {tool_file} 语法错误: {e}")
            results.append(False)
        except Exception as e:
            print(f"❌ {tool_file} 检查失败: {e}")
            results.append(False)
    
    return all(results)

def test_model_directory():
    """测试模型目录"""
    print("\n🤖 测试模型目录...")
    
    model_path = "./Qwen3-8B"
    if not os.path.exists(model_path):
        print(f"⚠️  模型目录不存在: {model_path}")
        print("   请确保已下载 Qwen3-8B 模型到此目录")
        return False
        
    # 检查关键文件
    key_files = ["config.json", "tokenizer.json", "tokenizer_config.json"]
    missing_files = []
    
    for key_file in key_files:
        file_path = os.path.join(model_path, key_file)
        if not os.path.exists(file_path):
            missing_files.append(key_file)
        else:
            print(f"✅ {key_file}")
    
    if missing_files:
        print(f"⚠️  模型目录缺少文件: {missing_files}")
        return False
    
    print("✅ 模型目录结构正确")
    return True

def main():
    """运行所有基础测试"""
    print("🧪 开始基础系统测试...")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("工具注册系统", test_tool_registry_import),
        ("配置文件", test_config_file),
        ("工具文件语法", test_tool_files),
        ("模型目录", test_model_directory)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed >= len(results) - 1:  # 允许模型目录测试失败
        print("🎉 基础测试通过！")
        print("\n📋 下一步:")
        print("1. 安装依赖: pip install modelscope torch transformers mcp python-docx psutil")
        print("2. 确保 Qwen3-8B 模型已下载到正确位置")
        print("3. 运行 'python start_qwen3_client.py' 启动客户端")
    else:
        print("⚠️  部分基础测试失败，请检查配置")
    
    return passed >= len(results) - 1

if __name__ == "__main__":
    main()
