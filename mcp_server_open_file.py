import subprocess
from mcp.server.fastmcp import FastMCP
from docx import Document
import logging

logger = logging.getLogger('open_file')

mcp = FastMCP("open_file")

@mcp.tool()
def open_file() -> dict:
    """ Always use the open file tool to complete the command to open the document,
    Use this tool when you need to know what is in the document."""

    # 检查WPS Office是否安装
    try:
        subprocess.run(['which', 'wps'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("WPS Office is installed.")
    except subprocess.CalledProcessError:
        print("WPS Office is not installed. Please install it first.")
        exit(1)

    filepath = '/home/<USER>/文档/文档测试.docx'

    # 在电脑上用wps打开特定filepath 文档
    try:
        subprocess.run(['wps', filepath], check=True)
        logger.info(f"Successfully opened {filepath} with WPS Office.")
    except subprocess.CalledProcessError as e:
        logger.info(f"Failed to open {filepath} with WPS Office: {e}")

    # 读取文档内容，并存入list中
    logger.info('start read file！！！')
    content = read_docx(filepath)
    logger.info(f'doc file content is:{content}')

    return {"success:": True, "result:": content}

def read_docx(file_path):
    try:
        # 打开文档
        doc = Document(file_path)
        # 读取每个段落并存储在列表中
        content = []
        for para in doc.paragraphs:
            content.append(para.text)
        return '\n'.join(content)
    except Exception as e:
        return f"An error occurred: {e}"


if __name__ == '__main__':
    mcp.run(transport="stdio")
    # filepath= '/home/<USER>/文档/文档测试.docx'
    # content = read_docx(filepath)
    # logger.info(f'doc file content is:{content}')