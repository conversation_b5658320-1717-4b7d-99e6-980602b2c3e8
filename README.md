# Qwen3 MCP Client

基于本地部署的 Qwen3 模型的 MCP (Model Context Protocol) 客户端，支持自动工具发现和注册。

## 功能特性

- 🤖 **本地 Qwen3 模型**: 使用您本地部署的 Qwen3-8B 模型
- 🔧 **自动工具发现**: 自动扫描 `tools/` 目录中的工具类代码
- 📦 **动态注册**: 工具可以自动注册到客户端中
- 🚀 **即插即用**: 只需编写工具代码，无需手动配置
- 💬 **智能对话**: 大模型可以根据需要自动调用合适的工具

## 项目结构

```
qwen3/
├── qwen3_test.py              # Qwen3 模型封装
├── qwen3_mcp_client.py        # 主要的 MCP 客户端
├── tool_registry.py           # 工具自动发现和注册系统
├── start_qwen3_client.py      # 启动脚本
├── servers_config.json        # MCP 服务器配置
├── mcp_server_open_file.py    # 示例：文件打开工具服务器
├── tools/                     # 工具目录
│   ├── __init__.py
│   ├── file_operations.py     # 文件操作工具集
│   └── system_info.py         # 系统信息工具集
└── Qwen3-8B/                  # Qwen3 模型文件
```

## 安装依赖

```bash
pip install modelscope torch transformers
pip install mcp python-docx psutil
```

## 快速开始

1. **启动客户端**:
   ```bash
   python start_qwen3_client.py
   ```

2. **与 AI 对话**:
   ```
   你: 帮我查看当前目录的文件
   助手: 我来帮您查看当前目录的文件...
   ```

3. **退出程序**:
   输入 `quit`、`exit` 或 `退出`

## 添加新工具

### 方法1: 在 tools/ 目录中创建新的工具文件

1. 在 `tools/` 目录中创建新的 Python 文件，例如 `my_tools.py`
2. 使用 `@tool` 装饰器标记工具函数
3. 创建 MCP 服务器实例

示例：

```python
# tools/my_tools.py
from mcp.server.fastmcp import FastMCP
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tool_registry import tool

# 创建MCP服务器实例
mcp = FastMCP("my_tools")

@mcp.tool()
@tool("计算两个数的和")
def add_numbers(a: int, b: int) -> dict:
    """计算两个数的和

    Args:
        a: 第一个数
        b: 第二个数

    Returns:
        包含计算结果的字典
    """
    result = a + b
    return {
        "success": True,
        "result": result,
        "operation": f"{a} + {b} = {result}"
    }

if __name__ == '__main__':
    mcp.run(transport="stdio")
```

### 方法2: 修改现有的 MCP 服务器

直接在现有的服务器文件（如 `mcp_server_open_file.py`）中添加新的工具函数。

## 现有工具

### 文件操作工具 (file_operations)
- `read_file`: 读取文件内容
- `write_file`: 写入文件内容
- `list_directory`: 列出目录内容
- `delete_path`: 删除文件或目录

### 系统信息工具 (system_info)
- `get_system_info`: 获取系统基本信息
- `get_cpu_info`: 获取CPU使用信息
- `get_memory_info`: 获取内存使用信息
- `get_disk_info`: 获取磁盘使用信息
- `get_network_info`: 获取网络接口信息
- `get_process_info`: 获取运行进程信息

### 文档操作工具 (open_file)
- `open_file`: 打开并读取 Word 文档

### 示例工具 (tool_template)
- `greet_user`: 向用户问候
- `calculate_circle_area`: 计算圆的面积
- `generate_random_number`: 生成随机数
- `analyze_text`: 文本统计分析

## 工作原理

1. **工具发现**: 启动时自动扫描 `tools/` 目录中的 Python 文件
2. **自动注册**: 识别带有 `@tool` 装饰器的函数并注册为 MCP 工具
3. **配置生成**: 自动更新 `servers_config.json` 配置文件
4. **服务器启动**: 为每个工具文件启动对应的 MCP 服务器
5. **智能调用**: Qwen3 模型根据用户需求自动选择和调用合适的工具

## 配置说明

### servers_config.json
```json
{
  "mcpServers": {
    "open_file": {
      "command": "python",
      "args": ["mcp_server_open_file.py"],
      "env": {}
    },
    "file_operations": {
      "command": "python",
      "args": ["tools/file_operations.py"],
      "env": {}
    }
  }
}
```

## 故障排除

1. **模型加载失败**: 确保 `Qwen3-8B/` 目录存在且包含完整的模型文件
2. **工具未发现**: 检查工具文件是否在 `tools/` 目录中，且使用了正确的装饰器
3. **服务器启动失败**: 检查 Python 环境和依赖是否正确安装

## 扩展功能

- 支持更多文件格式的处理
- 添加网络请求工具
- 集成数据库操作工具
- 支持图像处理工具
- 添加代码执行工具

## 许可证

MIT License
