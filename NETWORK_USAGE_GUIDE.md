# 网络化 Qwen3 MCP Client 使用指南

## 🎯 项目概述

这是一个基于 FastAPI 的网络化 MCP (Model Context Protocol) 客户端系统，具有以下特点：

- 🌐 **网络化架构**: 基于 HTTP API 的服务架构
- 🔧 **动态工具注册**: 工具服务器可以自动注册到客户端
- 📡 **分布式部署**: 客户端和工具服务器可以分别部署
- 🚀 **RESTful API**: 通过 HTTP 接口与客户端交互
- 🤖 **本地 Qwen3**: 使用本地部署的 Qwen3 模型

## 🏗️ 系统架构

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   Web Client    │ ──────────────► │  FastAPI MCP    │
│   (Browser)     │                │     Client      │
└─────────────────┘                │   (Port 8000)   │
                                   └─────────────────┘
                                           │
                                           │ Tool Registration
                                           │ & Execution
                                           ▼
                   ┌─────────────────┬─────────────────┬─────────────────┐
                   │  File Tools     │  System Tools   │  Custom Tools   │
                   │  Server         │  Server         │  Server         │
                   │  (Port 8001)    │  (Port 8002)    │  (Port 800X)    │
                   └─────────────────┴─────────────────┴─────────────────┘
```

## 🚀 快速开始

### 1. 安装依赖
```bash
python install_network_dependencies.py
```

### 2. 启动所有服务
```bash
python start_network_services.py
```

这将启动：
- FastAPI MCP Client (端口 8000)
- 文件操作工具服务器 (端口 8001)
- 系统信息工具服务器 (端口 8002)

### 3. 测试系统
```bash
python test_api_client.py
```

### 4. 使用 Web 界面
在浏览器中打开 `web_interface.html`

## 📡 API 接口

### 主要端点

#### 1. 聊天接口
```http
POST /chat
Content-Type: application/json

{
  "message": "查看系统信息",
  "use_tools": true,
  "conversation_id": "optional"
}
```

响应：
```json
{
  "response": "系统信息如下：...",
  "conversation_id": "conv_123456",
  "tool_calls": [
    {
      "tool_name": "get_system_info",
      "server_name": "system_info",
      "arguments": {}
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

#### 2. 工具注册接口
```http
POST /register-server
Content-Type: application/json

{
  "server_name": "my_tools",
  "server_url": "http://localhost:8003",
  "tools": [
    {
      "name": "my_tool",
      "description": "我的工具",
      "parameters": {
        "param1": {
          "type": "string",
          "description": "参数1",
          "required": true
        }
      }
    }
  ],
  "description": "我的工具服务器"
}
```

#### 3. 直接工具调用
```http
POST /call-tool
Content-Type: application/json

{
  "tool_name": "get_system_info",
  "server_name": "system_info",
  "arguments": {}
}
```

#### 4. 查看服务状态
```http
GET /health
GET /servers
GET /tools
```

## 🛠️ 创建自定义工具服务器

### 使用 NetworkToolServer 基类

```python
from network_tool_server import create_tool_server

# 创建工具服务器
server = create_tool_server(
    server_name="my_tools",
    port=8003,
    description="我的自定义工具服务器"
)

@server.tool("计算两个数的和")
def add_numbers(a: int, b: int) -> dict:
    """计算两个数的和"""
    return {
        "success": True,
        "result": a + b,
        "operation": f"{a} + {b} = {a + b}"
    }

@server.tool("获取当前时间")
def get_current_time() -> dict:
    """获取当前时间"""
    import datetime
    now = datetime.datetime.now()
    return {
        "success": True,
        "current_time": now.isoformat(),
        "formatted_time": now.strftime("%Y-%m-%d %H:%M:%S")
    }

if __name__ == "__main__":
    server.run()  # 自动注册到客户端
```

### 启动自定义工具服务器

```bash
python my_tool_server.py
```

服务器启动后会自动注册到 MCP Client，无需手动配置。

## 🔧 现有工具服务器

### 文件操作工具服务器 (端口 8001)
- `read_file` - 读取文件内容
- `write_file` - 写入文件内容
- `list_directory` - 列出目录内容
- `delete_path` - 删除文件或目录
- `create_directory` - 创建目录
- `copy_path` - 复制文件或目录
- `get_file_info` - 获取文件信息

### 系统信息工具服务器 (端口 8002)
- `get_system_info` - 获取系统基本信息
- `get_cpu_info` - 获取CPU使用信息
- `get_memory_info` - 获取内存使用信息
- `get_disk_info` - 获取磁盘使用信息
- `get_network_info` - 获取网络接口信息
- `get_process_info` - 获取运行进程信息
- `get_temperature_info` - 获取系统温度信息
- `get_battery_info` - 获取电池信息

## 💬 使用示例

### 通过 HTTP API
```bash
# 发送聊天消息
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "查看系统CPU使用情况", "use_tools": true}'

# 直接调用工具
curl -X POST "http://localhost:8000/call-tool" \
     -H "Content-Type: application/json" \
     -d '{"tool_name": "get_cpu_info", "server_name": "system_info", "arguments": {}}'
```

### 通过 Python 客户端
```python
import httpx
import asyncio

async def chat_with_ai():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/chat",
            json={
                "message": "列出当前目录的文件",
                "use_tools": True
            }
        )
        result = response.json()
        print(result["response"])

asyncio.run(chat_with_ai())
```

## 🔍 监控和调试

### 查看服务状态
```bash
# 检查所有服务
curl http://localhost:8000/health
curl http://localhost:8001/health
curl http://localhost:8002/health

# 查看已注册的服务器
curl http://localhost:8000/servers

# 查看所有可用工具
curl http://localhost:8000/tools
```

### API 文档
访问 `http://localhost:8000/docs` 查看完整的 API 文档。

## 🚀 部署建议

### 开发环境
- 所有服务运行在本地不同端口
- 使用 `start_network_services.py` 统一管理

### 生产环境
- 使用 Docker 容器化部署
- 配置负载均衡和服务发现
- 添加认证和授权机制
- 使用 HTTPS 加密通信

### 扩展部署
```bash
# 客户端服务器
uvicorn fastapi_mcp_client:app --host 0.0.0.0 --port 8000

# 工具服务器（可部署在不同机器）
python network_file_server.py --host 0.0.0.0 --port 8001
python network_system_server.py --host 0.0.0.0 --port 8002
```

## 🔧 配置说明

### client_config.json
```json
{
  "client_config": {
    "host": "0.0.0.0",
    "port": 8000,
    "model_path": "./Qwen3-8B",
    "max_tokens": 32768,
    "temperature": 0.7
  },
  "registered_servers": {},
  "server_registry_endpoint": "http://localhost:8000/register-server"
}
```

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认依赖是否正确安装
   - 查看错误日志

2. **工具注册失败**
   - 确认客户端服务正在运行
   - 检查网络连接
   - 验证注册端点URL

3. **模型加载失败**
   - 确认模型文件完整
   - 检查内存和显存是否足够
   - 验证模型路径配置

### 调试技巧
```bash
# 查看服务日志
python start_network_services.py

# 测试单个服务
python network_file_server.py
python network_system_server.py

# 测试API连接
python test_api_client.py
```

## 🎉 总结

这个网络化的 MCP Client 系统提供了：

- ✅ 灵活的分布式架构
- ✅ 简单的工具开发接口
- ✅ 自动化的服务注册
- ✅ 完整的 HTTP API
- ✅ 可视化的 Web 界面

通过这个系统，您可以轻松扩展 AI 的能力，让 Qwen3 模型不仅能够对话，还能执行各种实际任务！
