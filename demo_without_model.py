#!/usr/bin/env python3
"""
演示脚本 - 不加载实际模型，展示工具发现和注册功能
"""
import asyncio
import json
import logging
import os
import sys
from typing import List, Dict

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tool_registry import ToolRegistry
from qwen3_mcp_client import Configuration, Server, Tool

logging.basicConfig(level=logging.INFO)

class MockQwen3Client:
    """模拟的Qwen3客户端，用于演示"""
    
    def __init__(self):
        self.conversation_history = []
    
    def get_response(self, messages: List[Dict[str, str]]) -> str:
        """模拟LLM响应"""
        user_message = messages[-1]["content"].lower()
        
        # 简单的规则匹配来演示工具调用
        if "文件" in user_message or "file" in user_message:
            if "读取" in user_message or "read" in user_message:
                return json.dumps({
                    "tool": "read_file",
                    "arguments": {"file_path": "./README.md"}
                })
            elif "列表" in user_message or "list" in user_message:
                return json.dumps({
                    "tool": "list_directory", 
                    "arguments": {"dir_path": ".", "show_hidden": False}
                })
        
        elif "系统" in user_message or "system" in user_message:
            if "cpu" in user_message or "处理器" in user_message:
                return json.dumps({
                    "tool": "get_cpu_info",
                    "arguments": {}
                })
            elif "内存" in user_message or "memory" in user_message:
                return json.dumps({
                    "tool": "get_memory_info",
                    "arguments": {}
                })
            else:
                return json.dumps({
                    "tool": "get_system_info",
                    "arguments": {}
                })
        
        elif "进程" in user_message or "process" in user_message:
            return json.dumps({
                "tool": "get_process_info",
                "arguments": {"limit": 5}
            })
        
        # 默认响应
        return "我是一个演示版本的AI助手。我可以帮您使用以下工具：文件操作、系统信息查询等。请尝试说'查看系统信息'或'列出当前目录文件'。"

async def demo_tool_discovery():
    """演示工具发现功能"""
    print("🔍 演示工具发现功能")
    print("-" * 40)
    
    # 创建工具注册器
    registry = ToolRegistry()
    
    # 扫描工具
    discovered_tools = registry.scan_tools()
    
    print(f"✅ 发现 {len(discovered_tools)} 个工具:")
    for tool_name, tool_info in discovered_tools.items():
        print(f"  📋 {tool_name}")
        print(f"     描述: {tool_info['description'][:50]}...")
        print(f"     文件: {tool_info['file_path']}")
    
    # 更新配置
    registry.update_servers_config()
    print(f"\n✅ 配置文件已更新")
    
    return discovered_tools

async def demo_server_setup():
    """演示服务器设置"""
    print("\n🚀 演示服务器设置")
    print("-" * 40)
    
    # 加载配置
    config = Configuration()
    server_config = config.load_config("servers_config.json")
    
    print(f"✅ 加载配置成功，发现 {len(server_config['mcpServers'])} 个服务器:")
    
    servers = []
    for name, srv_config in server_config["mcpServers"].items():
        print(f"  🖥️  {name}: {srv_config['command']} {' '.join(srv_config['args'])}")
        servers.append(Server(name, srv_config))
    
    return servers

async def demo_chat_interaction():
    """演示聊天交互"""
    print("\n💬 演示聊天交互")
    print("-" * 40)
    
    # 创建模拟客户端
    mock_client = MockQwen3Client()
    
    # 演示对话
    demo_queries = [
        "查看系统信息",
        "列出当前目录文件", 
        "查看CPU信息",
        "查看内存使用情况",
        "显示运行进程"
    ]
    
    for query in demo_queries:
        print(f"\n👤 用户: {query}")
        
        messages = [
            {"role": "system", "content": "你是一个有用的助手"},
            {"role": "user", "content": query}
        ]
        
        response = mock_client.get_response(messages)
        print(f"🤖 助手: {response}")
        
        # 尝试解析工具调用
        try:
            tool_call = json.loads(response)
            if "tool" in tool_call:
                print(f"   🔧 将调用工具: {tool_call['tool']}")
                print(f"   📝 参数: {tool_call['arguments']}")
        except json.JSONDecodeError:
            print(f"   💭 普通回复")

async def main():
    """主演示函数"""
    print("🎭 Qwen3 MCP Client 功能演示")
    print("=" * 60)
    print("这是一个演示版本，展示工具发现和注册功能")
    print("实际版本需要安装完整依赖并加载Qwen3模型")
    print("=" * 60)
    
    try:
        # 演示工具发现
        await demo_tool_discovery()
        
        # 演示服务器设置
        await demo_server_setup()
        
        # 演示聊天交互
        await demo_chat_interaction()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("\n📋 要运行完整版本:")
        print("1. 运行: python install_dependencies.py")
        print("2. 确保Qwen3-8B模型已下载")
        print("3. 运行: python start_qwen3_client.py")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
