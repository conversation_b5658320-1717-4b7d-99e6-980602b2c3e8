#!/usr/bin/env python3
"""
Qwen3 MCP Client 启动脚本
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qwen3_mcp_client import main

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 启动 Qwen3 MCP Client")
    print("=" * 60)
    print("正在初始化本地 Qwen3 模型...")
    print("正在扫描和注册工具...")
    print("请稍候...")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        logging.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
